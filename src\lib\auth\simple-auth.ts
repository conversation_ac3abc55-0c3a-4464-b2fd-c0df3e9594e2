"use client"

import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface User {
  id: string
  email: string
  name?: string
  role: string
  isActive: boolean
}

interface AuthState {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
  signIn: (email: string, password: string) => Promise<void>
  signOut: () => void
  setUser: (user: User | null) => void
}

// Store Zustand pour l'authentification
export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      isLoading: false,
      isAuthenticated: false,

      signIn: async (email: string, password: string) => {
        set({ isLoading: true })
        
        try {
          // Simulation d'une API de connexion
          await new Promise(resolve => setTimeout(resolve, 1000))
          
          // Utilisateurs de test
          const testUsers: Record<string, User> = {
            '<EMAIL>': {
              id: '1',
              email: '<EMAIL>',
              name: 'Administrateur',
              role: 'S<PERSON>ER_ADMIN',
              isActive: true
            },
            '<EMAIL>': {
              id: '2',
              email: '<EMAIL>',
              name: 'Gestionnaire',
              role: 'MANAGER',
              isActive: true
            },
            '<EMAIL>': {
              id: '3',
              email: '<EMAIL>',
              name: 'Auditeur',
              role: 'AUDITOR',
              isActive: true
            },
            '<EMAIL>': {
              id: '4',
              email: '<EMAIL>',
              name: 'Utilisateur',
              role: 'USER',
              isActive: true
            }
          }

          const user = testUsers[email]
          
          if (!user || password !== 'password123') {
            throw new Error('Email ou mot de passe incorrect')
          }

          set({ 
            user, 
            isAuthenticated: true, 
            isLoading: false 
          })
        } catch (error) {
          set({ isLoading: false })
          throw error
        }
      },

      signOut: () => {
        set({ 
          user: null, 
          isAuthenticated: false 
        })
      },

      setUser: (user: User | null) => {
        set({ 
          user, 
          isAuthenticated: !!user 
        })
      }
    }),
    {
      name: 'magneto-auth',
      partialize: (state) => ({ 
        user: state.user, 
        isAuthenticated: state.isAuthenticated 
      })
    }
  )
)

// Hook pour utiliser l'authentification
export function useSimpleAuth() {
  return useAuthStore()
}

// Hook pour les permissions basé sur le store
export function useSimplePermissions() {
  const { user } = useAuthStore()
  const userRole = user?.role

  const checkPermission = (permission: string): boolean => {
    if (!userRole) return false

    // Permissions par rôle
    const permissions: Record<string, string[]> = {
      SUPER_ADMIN: ['*'],
      ADMIN: ['users:*', 'audits:*', 'observations:*', 'reports:*'],
      MANAGER: ['users:read', 'audits:*', 'observations:*', 'reports:*'],
      AUDITOR: ['audits:read', 'audits:update', 'observations:read', 'observations:create', 'observations:update', 'reports:read'],
      USER: ['audits:read', 'observations:read', 'reports:read']
    }

    const userPermissions = permissions[userRole] || []
    return userPermissions.includes('*') || userPermissions.includes(permission)
  }

  return {
    userRole,
    checkPermission,
    canManageUsers: checkPermission('users:read'),
    canCreateUsers: checkPermission('users:create'),
    canUpdateUsers: checkPermission('users:update'),
    canDeleteUsers: checkPermission('users:delete'),
    canManageAudits: checkPermission('audits:read'),
    canCreateAudits: checkPermission('audits:create'),
    canUpdateAudits: checkPermission('audits:update'),
    canDeleteAudits: checkPermission('audits:delete'),
    canManageObservations: checkPermission('observations:read'),
    canCreateObservations: checkPermission('observations:create'),
    canUpdateObservations: checkPermission('observations:update'),
    canDeleteObservations: checkPermission('observations:delete'),
    canManageReports: checkPermission('reports:read'),
    canCreateReports: checkPermission('reports:create'),
    canUpdateReports: checkPermission('reports:update'),
    canDeleteReports: checkPermission('reports:delete'),
    isAdmin: userRole === 'SUPER_ADMIN' || userRole === 'ADMIN',
    isSuperAdmin: userRole === 'SUPER_ADMIN',
    isManager: userRole === 'MANAGER',
    isAuditor: userRole === 'AUDITOR',
  }
}
